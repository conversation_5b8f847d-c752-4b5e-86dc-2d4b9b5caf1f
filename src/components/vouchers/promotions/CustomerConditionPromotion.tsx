import React, { useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  IconButton,
  Pagination,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import TitleDialog from "../../dialog/TitleDialog";
import BoxImage from "../../box-image";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import SearchGroupPromotion from "./SearchGroupPromotion";
import SearchCustomerPromotion from "./SearchCustomerPromotion";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useUser } from "@/src/api/hooks/user/use-user";
import { useUserGroup } from "@/src/api/hooks/user-group/use-user-group";
import { TYPE_DISTRIBUTION } from "@/src/api/types/voucher.type";

export default function CustomerConditionPromotion({ voucher }) {
  const {
    setValue,
    watch,
    control,
    formState: { errors },
  } = useFormContext<any>(); // Use context to get control
  const conditionType = watch("conditionType");
  const distributionType = watch("distributionType");
  const codeType = watch("codeType");

  // Disable customer condition when distribution type is IMMEDIATE for unique vouchers
  const isDisabled = codeType === "Unique" && distributionType === TYPE_DISTRIBUTION.IMMEDIATE;

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<string>("");
  const [selectedGroup, setSelectedGroup] = useState<any>();
  const [selectedCustomers, setSelectedCustomers] = useState([]);
  const chooseText =
    conditionType == "Customer"
      ? "Chọn khách hàng"
      : conditionType == "Group"
      ? "Chọn nhóm khách hàng"
      : null;
  const [currentData, setCurrentData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const recordsPerPage = 5; // Số bản ghi mỗi trang
  const totalRecords = selectedCustomers.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const storeId = useStoreId();
  const { listUserByUserIds } = useUser();
  const { getListUserGroupById } = useUserGroup();

  useEffect(() => {
    const startIndex = (currentPage - 1) * recordsPerPage;
    const endIndex = currentPage * recordsPerPage;
    setCurrentData(selectedCustomers.slice(startIndex, endIndex));
  }, [currentPage, selectedCustomers]);
  // Hàm gọi khi thay đổi trang
  const handlePageChange = (_event: any, pageNumber: number) => {
    setCurrentPage(pageNumber);
  };
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const clickOpenDialog = () => {
    setOpenDialog(true);
  };

  const removeGroupName = () => {
    setSelectedGroupId("");
    setValue("userGroupId", ""); // Update value in form field
  };

  const handleSubmitGroup = async (groupData: any) => {
    // groupData can be either a string (groupName) or an object with groupId and groupName
    if (typeof groupData === "string") {
      // Backward compatibility - if only groupName is passed
      setSelectedGroupId(groupData);
      setValue("userGroupId", groupData); // Update value in form field
    } else {
      // New format - groupData is an object with groupId and groupName
      setSelectedGroupId(groupData.groupId);
      setValue("userGroupId", groupData.groupId); // Store groupId in form
    }
    handleCloseDialog();
  };

  const handleSubmitCustomer = async (customers: any[]) => {
    // Update the selected customers with the new selection from dialog
    setSelectedCustomers(customers);

    // Update the form with the new customer IDs
    const userIds = customers.map((customer: any) => customer.userId);
    setValue("userIds", userIds);

    handleCloseDialog();
  };

  const removeCustomer = (userId: string) => {
    // Lọc danh sách các khách hàng không trùng với userId cần xóa
    const updatedCustomers = selectedCustomers.filter((customer) => customer.userId !== userId);

    // Cập nhật danh sách khách hàng được chọn
    setSelectedCustomers(updatedCustomers);

    // Cập nhật giá trị 'userIds' trong form (hoặc bất kỳ logic liên quan nào khác)
    setValue(
      "userIds",
      updatedCustomers.map((customer) => customer.userId)
    );

    // Cập nhật lại trang nếu xóa hết tất cả bản ghi ở trang hiện tại
    if (
      currentPage > 1 &&
      updatedCustomers.length <= currentPage * recordsPerPage - recordsPerPage
    ) {
      setCurrentPage(currentPage - 1); // Chuyển về trang trước nếu không còn bản ghi ở trang hiện tại
    }
  };

  const fetchUserByUserIds = async (userIds: string[], shopId: string) => {
    const response = await listUserByUserIds({ shopId: shopId, userIds: userIds });
    if (response?.data?.data) {
      setSelectedCustomers(response?.data?.data);
    }
  };

  const fetchGroupInfo = async (groupId: string, shopId: string) => {
    try {
      const response = await getListUserGroupById(shopId, groupId);
      if (response?.data) {
        const group = response.data;
        setSelectedGroup(group);
      }
    } catch (error) {
      console.error("Error fetching group info:", error);
    }
  };

  // Initialize form values and local states when voucher changes
  useEffect(() => {
    if (voucher) {
      // Set conditionType if available
      if (voucher.conditionType) {
        setValue("conditionType", voucher.conditionType);
      }

      // Set userGroupId if available
      if (voucher.userGroupId) {
        setValue("userGroupId", voucher.userGroupId);
        setSelectedGroupId(voucher.userGroupId);
      }

      // Set userIds if available
      if (voucher.userIds && Array.isArray(voucher.userIds)) {
        setValue("userIds", voucher.userIds);
      }
    }
  }, [voucher, setValue, storeId]);

  // Handle customer data fetching
  useEffect(() => {
    if (voucher && voucher.conditionType === "Customer" && voucher.userIds && storeId) {
      fetchUserByUserIds(voucher.userIds, storeId);
    }
  }, [voucher, storeId]);

  // Fetch group name when selectedGroupId changes
  useEffect(() => {
    if (selectedGroupId && storeId) {
      fetchGroupInfo(selectedGroupId, storeId);
    }
  }, [selectedGroupId, storeId]);

  // Clear related fields when conditionType changes
  useEffect(() => {
    if (conditionType === "All") {
      // Clear group and customer selections when "All" is selected
      setValue("userGroupId", "");
      setValue("userIds", []);
      setSelectedGroupId("");
      setSelectedCustomers([]);
    } else if (conditionType === "Group") {
      // Clear customer selections when "Group" is selected
      setValue("userIds", []);
      setSelectedCustomers([]);
      // Don't clear group selections - they should be preserved
    } else if (conditionType === "Customer") {
      // Clear group selections when "Customer" is selected
      setValue("userGroupId", "");
      setSelectedGroupId("");
    }
  }, [conditionType, setValue]);

  return (
    <Box>
      {!isDisabled ? (
        <>
          <FormControl>
            <FormLabel>Điều kiện của khách hàng</FormLabel>
            <Controller
              name="conditionType"
              control={control}
              rules={{ required: "Please select an option" }} // Validation rule
              render={({ field }) => (
                <RadioGroup {...field}>
                  <FormControlLabel value="All" control={<Radio />} label="Tất cả" />
                  <FormControlLabel
                    value="Group"
                    control={<Radio />}
                    label="Chỉ định nhóm khách hàng"
                  />
                  <FormControlLabel
                    value="Customer"
                    control={<Radio />}
                    label="Chỉ định khách hàng"
                  />
                </RadioGroup>
              )}
            />
            {errors.conditionType && (
              <span style={{ color: "red" }}>{errors.conditionType?.message}</span>
            )}
            {/* Display error message */}
          </FormControl>

          {/* Hidden Controller for userGroupId to ensure proper validation */}
          <Controller
            name="userGroupId"
            control={control}
            render={({ field }) => <input type="hidden" {...field} />}
          />

          {/* Hidden Controller for userIds to ensure proper validation */}
          <Controller
            name="userIds"
            control={control}
            render={({ field }) => <input type="hidden" {...field} />}
          />

          {chooseText && (
            <>
              <Box
                sx={{ backgroundColor: "neutral.50", padding: 2, borderRadius: 1, width: "100%" }}
              >
                <Button onClick={clickOpenDialog}>{chooseText}</Button>
              </Box>
              <Box>
                {conditionType == "Group" && selectedGroupId && (
                  <Box display="flex" marginTop={1} justifyContent="space-between">
                    <Typography>{selectedGroup?.groupName}</Typography>

                    <IconButton onClick={removeGroupName}>
                      <DeleteOutlineOutlinedIcon />
                    </IconButton>
                  </Box>
                )}

                {conditionType === "Group" && errors.userGroupId && (
                  <Typography variant="subtitle2" color="red" marginTop={1}>
                    {errors.userGroupId?.message}
                  </Typography>
                )}

                {conditionType == "Customer" &&
                  selectedCustomers &&
                  selectedCustomers.length > 0 &&
                  currentData.map((user, index) => {
                    return (
                      <Box key={user.userId}>
                        <Box display="flex" marginTop={1} justifyContent="space-between">
                          <Box>
                            <Typography>{user.fullname}</Typography>
                            <Typography variant="subtitle2" color="text.secondary">
                              {user.phoneNumber}
                            </Typography>
                            <Typography variant="subtitle2" color="text.secondary">
                              {user.email}
                            </Typography>
                          </Box>

                          <IconButton onClick={() => removeCustomer(user.userId)}>
                            <DeleteOutlineOutlinedIcon />
                          </IconButton>
                        </Box>
                        <Divider />
                      </Box>
                    );
                  })}
                {selectedCustomers.length > 0 && (
                  <Box display="flex" justifyContent="center" marginTop={2}>
                    <Pagination
                      count={totalPages}
                      page={currentPage}
                      onChange={handlePageChange}
                      color="primary"
                      shape="rounded"
                    />
                  </Box>
                )}

                {conditionType === "Customer" && errors.userIds && (
                  <Typography variant="subtitle2" color="red" marginTop={1}>
                    {errors.userIds?.message}
                  </Typography>
                )}
              </Box>
              <TitleDialog
                title={chooseText}
                open={openDialog}
                handleClose={handleCloseDialog}
                submitBtnTitle="Xác nhận"
                showActionDialog={false}
                maxWidth="xl"
              >
                {conditionType == "Group" ? (
                  <SearchGroupPromotion
                    handleSubmit={handleSubmitGroup}
                    handleClose={handleCloseDialog}
                    selectedGroupId={selectedGroupId}
                  />
                ) : (
                  <SearchCustomerPromotion
                    handleSubmit={handleSubmitCustomer}
                    handleClose={handleCloseDialog}
                    selectedCustomers={selectedCustomers}
                  />
                )}
              </TitleDialog>
            </>
          )}
        </>
      ) : (
        <Box>
          <Typography variant="body2" color="text.secondary">
            Khi phát hành ngay lập tức, voucher sẽ áp dụng cho tất cả khách hàng
          </Typography>
        </Box>
      )}
    </Box>
  );
}
